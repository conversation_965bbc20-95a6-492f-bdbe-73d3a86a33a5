import { IProduct } from '../types/inventory/IProduct';

const isServerEnvironment = () => typeof window === 'undefined';

export class ProductApiService {
  static async getAllProducts(): Promise<IProduct[]> {
    if (isServerEnvironment()) {
      const res = await fetch('/api/products');
      return res.json();
    } else {
      // Qui puoi mantenere i mock solo per test unitari
      throw new Error('Mock non disponibile in produzione');
    }
  }

  static async createProduct(data: Omit<IProduct, 'id'>): Promise<IProduct> {
    if (isServerEnvironment()) {
      const res = await fetch('/api/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return res.json();
    } else {
      throw new Error('Mock non disponibile in produzione');
    }
  }
}
