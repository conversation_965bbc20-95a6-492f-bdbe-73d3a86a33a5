/**
 * API Routes per la gestione degli appuntamenti
 *
 * Queste routes gestiscono tutte le operazioni CRUD sugli appuntamenti
 * utilizzando Prisma per interagire con il database PostgreSQL.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { AppointmentDatabaseService } from '../package/src/services/AppointmentDatabaseService';
import { ICreateAppointment } from '../package/src/types/appointments/IAppointment';

/**
 * Handler principale per le API degli appuntamenti
 * GET /api/appointments - Ottiene tutti gli appuntamenti
 * POST /api/appointments - Crea un nuovo appuntamento
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'POST') {
      // Creazione nuovo appuntamento
      const appointmentData: ICreateAppointment = req.body;

      // Validazione base dei dati richiesti
      if (!appointmentData.patientId || !appointmentData.title || !appointmentData.startTime || !appointmentData.endTime) {
        return res.status(400).json({
          success: false,
          error: '<PERSON><PERSON> mancanti: patientId, title, startTime e endTime sono obbligatori'
        });
      }

      // Conversione delle date da string a Date se necessario
      const processedData: ICreateAppointment = {
        ...appointmentData,
        startTime: new Date(appointmentData.startTime),
        endTime: new Date(appointmentData.endTime)
      };

      const appointment = await AppointmentDatabaseService.createAppointment(processedData);

      return res.status(201).json({
        success: true,
        data: appointment
      });

    } else if (req.method === 'GET') {
      // Recupero di tutti gli appuntamenti
      const appointments = await AppointmentDatabaseService.getAllAppointments();

      return res.status(200).json({
        success: true,
        data: appointments,
        count: appointments.length
      });

    } else {
      // Metodo non supportato
      return res.status(405).json({
        success: false,
        error: `Metodo ${req.method} non supportato`
      });
    }

  } catch (error) {
    console.error('❌ Errore API appuntamenti:', error);

    return res.status(500).json({
      success: false,
      error: 'Errore interno del server durante la gestione degli appuntamenti'
    });
  }
}
