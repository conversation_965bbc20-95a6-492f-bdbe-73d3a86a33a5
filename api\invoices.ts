import { InvoiceDatabaseService } from '@/services/InvoiceDatabaseService';

export default async (req, res) => {
  if (req.method === 'POST') {
    const data = await req.json();
    const invoice = await InvoiceDatabaseService.createInvoice(data);
    return new Response(JSON.stringify(invoice), { headers: { 'Content-Type': 'application/json' } });
  }
  // GET all
  const list = await InvoiceDatabaseService.getAllInvoices();
  return new Response(JSON.stringify(list), { headers: { 'Content-Type': 'application/json' } });
};
