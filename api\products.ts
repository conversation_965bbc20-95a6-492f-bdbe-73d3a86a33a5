import { ProductDatabaseService } from '@/services/ProductDatabaseService';

export default async (req, res) => {
  if (req.method === 'POST') {
    const data = await req.json();
    const product = await ProductDatabaseService.createProduct(data);
    return new Response(JSON.stringify(product), { headers: { 'Content-Type': 'application/json' } });
  }
  // GET all
  const list = await ProductDatabaseService.getAllProducts();
  return new Response(JSON.stringify(list), { headers: { 'Content-Type': 'application/json' } });
};
