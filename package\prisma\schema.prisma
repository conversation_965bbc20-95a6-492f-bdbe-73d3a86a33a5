// <PERSON><PERSON> (Inventario)
model Product {
  id          Int      @id @default(autoincrement())
  name        String
  category    String
  description String?
  quantity    Int
  unit        String
  minQuantity Int      @map("min_quantity")
  price       Decimal  @db.Decimal(10, 2)
  supplier    String?
  location    String?
  notes       String?
  lastOrder   DateTime? @map("last_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("products")
}
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modello Paziente
model Patient {
  id          String   @id @default(cuid())
  firstName   String   @map("first_name")
  lastName    String   @map("last_name")
  email       String?  @unique
  phone       String?
  dateOfBirth DateTime? @map("date_of_birth")
  address     String?
  city        String?
  postalCode  String?  @map("postal_code")
  fiscalCode  String?  @unique @map("fiscal_code")
  notes       String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relazioni
  appointments Appointment[]
  invoices     Invoice[]
  patientUDIs  PatientUDI[]
  files        File[]

  @@map("patients")
}

// Modello UDI (Unique Device Identifier) per dispositivi medici
model UDI {
  id          String   @id @default(cuid())
  deviceName  String   @map("device_name")
  udiCode     String   @unique @map("udi_code")
  manufacturer String?
  model       String?
  lotNumber   String?  @map("lot_number")
  expiryDate  DateTime? @map("expiry_date")
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relazioni
  patientUDIs PatientUDI[]

  @@map("udis")
}

// Tabella di collegamento Paziente-UDI
model PatientUDI {
  id           String   @id @default(cuid())
  patientId    String   @map("patient_id")
  udiId        String   @map("udi_id")
  implantDate  DateTime @map("implant_date")
  position     String?  // Posizione nell'arcata dentale
  notes        String?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relazioni
  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
  udi     UDI     @relation(fields: [udiId], references: [id], onDelete: Cascade)

  @@unique([patientId, udiId])
  @@map("patient_udis")
}

// Modello Appuntamento
model Appointment {
  id          String            @id @default(cuid())
  patientId   String            @map("patient_id")
  title       String
  description String?
  startTime   DateTime          @map("start_time")
  endTime     DateTime          @map("end_time")
  status      AppointmentStatus @default(SCHEDULED)
  type        String?           // Tipo di trattamento
  notes       String?
  createdAt   DateTime          @default(now()) @map("created_at")
  updatedAt   DateTime          @updatedAt @map("updated_at")

  // Relazioni
  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@map("appointments")
}

// Enum per lo status degli appuntamenti
enum AppointmentStatus {
  SCHEDULED   // Programmato
  CONFIRMED   // Confermato
  IN_PROGRESS // In corso
  COMPLETED   // Completato
  CANCELLED   // Cancellato
  NO_SHOW     // Paziente non si è presentato
}

// Modello Fattura
model Invoice {
  id          String        @id @default(cuid())
  patientId   String        @map("patient_id")
  invoiceNumber String      @unique @map("invoice_number")
  issueDate   DateTime      @map("issue_date")
  dueDate     DateTime?     @map("due_date")
  amount      Decimal       @db.Decimal(10, 2)
  taxAmount   Decimal?      @map("tax_amount") @db.Decimal(10, 2)
  totalAmount Decimal       @map("total_amount") @db.Decimal(10, 2)
  status      InvoiceStatus @default(DRAFT)
  description String?
  notes       String?
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")

  // Relazioni
  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@map("invoices")
}

// Enum per lo status delle fatture
enum InvoiceStatus {
  DRAFT     // Bozza
  SENT      // Inviata
  PAID      // Pagata
  OVERDUE   // Scaduta
  CANCELLED // Cancellata
}

// Modello File per documenti e immagini
model File {
  id          String   @id @default(cuid())
  patientId   String?  @map("patient_id") // Opzionale, può essere un file generale
  fileName    String   @map("file_name")
  originalName String  @map("original_name")
  mimeType    String   @map("mime_type")
  size        Int      // Dimensione in bytes
  path        String   // Percorso del file
  category    FileCategory @default(OTHER)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relazioni
  patient Patient? @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@map("files")
}

// Enum per le categorie di file
enum FileCategory {
  XRAY        // Radiografie
  PHOTO       // Foto
  DOCUMENT    // Documenti
  CONSENT     // Consensi informati
  TREATMENT   // Piani di trattamento
  OTHER       // Altri
}

// Modello Notifiche
model Notification {
  id        String             @id @default(cuid())
  title     String
  message   String
  type      NotificationType   @default(INFO)
  isRead    Boolean            @default(false) @map("is_read")
  userId    String?            @map("user_id") // Per future implementazioni multi-utente
  createdAt DateTime           @default(now()) @map("created_at")
  updatedAt DateTime           @updatedAt @map("updated_at")

  @@map("notifications")
}

// Enum per i tipi di notifica
enum NotificationType {
  INFO      // Informativa
  WARNING   // Avviso
  ERROR     // Errore
  SUCCESS   // Successo
  REMINDER  // Promemoria
}
